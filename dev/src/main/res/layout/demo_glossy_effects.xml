<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FF1A1A1A"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="@dimen/dp_32">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_24"
        android:text="毛玻璃高光效果演示"
        android:textColor="@color/white"
        android:textSize="18sp"
        android:textStyle="bold" />

    <!-- 基础毛玻璃效果 -->
    <View
        android:layout_width="120dp"
        android:layout_height="80dp"
        android:layout_marginBottom="@dimen/dp_16"
        android:background="@drawable/bg_glossy_oval_effect" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_24"
        android:text="基础毛玻璃效果"
        android:textColor="@color/white_70"
        android:textSize="12sp" />

    <!-- 增强毛玻璃效果 -->
    <View
        android:layout_width="120dp"
        android:layout_height="80dp"
        android:layout_marginBottom="@dimen/dp_16"
        android:background="@drawable/bg_glossy_oval_enhanced" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_24"
        android:text="增强高光效果"
        android:textColor="@color/white_70"
        android:textSize="12sp" />

    <!-- 可自定义毛玻璃效果 -->
    <View
        android:layout_width="120dp"
        android:layout_height="80dp"
        android:layout_marginBottom="@dimen/dp_16"
        android:background="@drawable/bg_glossy_oval_customizable" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="可自定义颜色效果"
        android:textColor="@color/white_70"
        android:textSize="12sp" />

</LinearLayout>
