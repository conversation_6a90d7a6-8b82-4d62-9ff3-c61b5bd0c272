<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 透明内部 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="@color/transparent" />
        </shape>
    </item>

    <!-- 金属边框 - 顶部高光 -->
    <item>
        <shape android:shape="oval">
            <stroke
                android:width="3dp">
                <gradient
                    android:angle="270"
                    android:startColor="#FFFFFF"
                    android:centerColor="#B0FFFFFF"
                    android:endColor="#40FFFFFF" />
            </stroke>
        </shape>
    </item>

    <!-- 金属边框 - 底部阴影 -->
    <item
        android:bottom="1dp"
        android:left="1dp"
        android:right="1dp"
        android:top="1dp">
        <shape android:shape="oval">
            <stroke
                android:width="2dp">
                <gradient
                    android:angle="90"
                    android:startColor="#30FFFFFF"
                    android:centerColor="#80FFFFFF"
                    android:endColor="#D0FFFFFF" />
            </stroke>
        </shape>
    </item>

</layer-list>
