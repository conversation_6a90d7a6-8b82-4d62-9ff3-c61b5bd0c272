<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 底层毛玻璃背景 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="#40FFFFFF" />
        </shape>
    </item>

    <!-- 高光渐变层 -->
    <item
        android:bottom="8dp"
        android:left="8dp"
        android:right="8dp"
        android:top="8dp">
        <shape android:shape="oval">
            <gradient
                android:angle="270"
                android:centerColor="#66FFFFFF"
                android:endColor="#10FFFFFF"
                android:startColor="#80FFFFFF"
                android:type="radial"
                android:gradientRadius="50%p" />
        </shape>
    </item>

    <!-- 内部高光 -->
    <item
        android:bottom="16dp"
        android:left="16dp"
        android:right="16dp"
        android:top="16dp">
        <shape android:shape="oval">
            <gradient
                android:angle="45"
                android:centerColor="#30FFFFFF"
                android:endColor="@color/transparent"
                android:startColor="#60FFFFFF" />
        </shape>
    </item>

    <!-- 边缘光晕 -->
    <item>
        <shape android:shape="oval">
            <stroke
                android:width="1dp"
                android:color="#20FFFFFF" />
        </shape>
    </item>

</layer-list>
